package com.bc.iap.web.service;

import com.bc.iap.web.common.vo.HomepageVo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 短剧服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
public class ShortDramasServiceTest {

    @Resource
    private IShortDramasService shortDramasService;

    /**
     * 测试根据分类ID获取短剧数据
     */
    @Test
    public void testGetShortDramasByCategoryIds() {
        // 测试数据：分类ID列表
        List<Integer> categoryIds = Arrays.asList(1, 2, 3, 4, 5);
        String siteDomain = "vidfeedly.com";

        try {
            // 调用服务方法
            HomepageVo result = shortDramasService.getShortDramasByCategoryIds(categoryIds, siteDomain);
            
            // 验证结果
            System.out.println("测试结果:");
            System.out.println("站点域名: " + result.getSiteDomain());
            System.out.println("分类数量: " + (result.getPlateList() != null ? result.getPlateList().size() : 0));
            
            if (result.getPlateList() != null) {
                for (HomepageVo.Plate plate : result.getPlateList()) {
                    System.out.println("分类名称: " + plate.getPlateName() + 
                                     ", 短剧数量: " + (plate.getPlateList() != null ? plate.getPlateList().size() : 0));
                }
            }
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试空分类ID列表的情况
     */
    @Test
    public void testGetShortDramasWithEmptyCategoryIds() {
        List<Integer> categoryIds = Arrays.asList();
        String siteDomain = "vidfeedly.com";

        try {
            HomepageVo result = shortDramasService.getShortDramasByCategoryIds(categoryIds, siteDomain);
            System.out.println("空分类ID测试不应该到达这里");
        } catch (Exception e) {
            System.out.println("预期的异常: " + e.getMessage());
        }
    }
}
