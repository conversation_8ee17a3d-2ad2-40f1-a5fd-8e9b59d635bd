package com.bc.iap.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVoCategoryName;
import com.bc.iap.web.entity.ShortDramas;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 短剧主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

public interface ShortDramasMapper extends BaseMapper<ShortDramas> {
    @Select("SELECT short_dramas_v1.title, episodes_v1.drama_id, short_dramas_v1.thumbnail_url, short_dramas_v1.description,short_dramas_v1.rating, short_dramas_v1.views_count, short_dramas_v1.likes_count, COUNT(episodes_v1.drama_id) AS total_count, (SELECT COUNT(*) FROM episodes_v1 WHERE drama_id = #{dramaId} AND episodes_v1.is_vip = 0) AS free_count " +
            "FROM short_dramas_v1 " +
            "INNER JOIN episodes_v1 " +
            "ON short_dramas_v1.drama_id = episodes_v1.drama_id " +
            "WHERE short_dramas_v1.drama_id = #{dramaId}"
    )
    EpisodesVo selectByDramaId(@Param("dramaId") Integer dramaId);

    @Select("SELECT " +
            "    short_dramas_v1.md5_id, " +
            "    episodes_v1.episode_id, " +
            "    episodes_v1.drama_id, " +
            "    episodes_v1.episode_number, " +
            "    episodes_v1.title as episode_title, " +
            "    short_dramas_v1.title as drama_title, " +
            "    episodes_v1.release_date, " +
            "    episodes_v1.duration, " +
            "    short_dramas_v1.thumbnail_url, " +
            "    episodes_v1.video_url, " +
            "    short_dramas_v1.views_count, " +
            "    short_dramas_v1.likes_count, " +
            "    episodes_v1.word_url, " +
            "    short_dramas_v1.description, " +
            "    episodes_v1.is_vip, " +
            "    (SELECT COUNT(*) FROM episodes_v1 WHERE drama_id = #{dramaId}) AS total_count, " +
            "    (SELECT COUNT(*) FROM episodes_v1 WHERE drama_id = #{dramaId} AND episodes_v1.is_vip = 0) AS free_count " +
            "FROM episodes_v1 " +
            "INNER JOIN short_dramas_v1 " +
            "    ON short_dramas_v1.drama_id = episodes_v1.drama_id " +
            "WHERE episodes_v1.drama_id = #{dramaId} " +
            "    AND episodes_v1.episode_number = #{index}"
    )
    EpisodesDetailVo selectDetailByDramaId(@Param("dramaId") String dramaId, @Param("index") Integer index);

    @Select("SELECT " +
            "short_dramas_v1.*, " +
            "drama_categories.category_id, " +
            "categories.name " +
            "FROM short_dramas_v1 " +
            "INNER JOIN drama_categories " +
            "ON short_dramas_v1.md5_id = drama_categories.drama_id " +
            "INNER JOIN categories " +
            "ON drama_categories.category_id = categories.category_id " +
            "WHERE categories.status = 1 " +
            "ORDER BY short_dramas_v1.drama_id")
    List<HomepageVoCategoryName> homepage();
}
