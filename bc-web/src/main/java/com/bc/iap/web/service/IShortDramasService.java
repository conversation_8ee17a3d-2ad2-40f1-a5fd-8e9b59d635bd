package com.bc.iap.web.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.common.dto.exception.BaseException;
import com.bc.iap.user.mapper.TUserAssetInfoMapper;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.entity.TUserAssetInfo;
import com.bc.iap.user.model.resp.UserTokenResp;
import com.bc.iap.user.service.UserService;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.HomepageReq;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVo;
import com.bc.iap.web.common.vo.HomepageVoCategoryName;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.mapper.ShortDramasMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 短剧主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class IShortDramasService {

    private final ShortDramasMapper shortDramasMapper;
    private final UserService userService;
    private final TUserAssetInfoMapper TUserAssetInfoMapper;

    public HomepageVo homepage(HomepageReq req) {
        // 定义好需要展示的分类id [] 形式
        List<Integer> categoryIds = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8);

        if (req.getSiteDomain() == null) {
            throw new BaseException(ErrorCode.FAILURE, "siteDomain is null");
        }
        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(req.getSiteDomain());

        // 查出categoryIds下的所有短剧
        List<HomepageVoCategoryName> result = shortDramasMapper.homepageByCategoryIds(categoryIds);
        log.info("根据分类ID{}查询到{}条短剧-分类关联记录", categoryIds, result.size());

        // 按分类名称分组，每个分类包含其所有短剧
        Map<String, List<HomepageVoCategoryName>> groupedByCategory = result.stream()
                .collect(Collectors.groupingBy(HomepageVoCategoryName::getName));

        log.info("共有{}个分类", groupedByCategory.size());

        // 构建Plate列表
        List<HomepageVo.Plate> plateList = groupedByCategory.entrySet().stream()
                .map(entry -> {
                    HomepageVo.Plate plate = new HomepageVo.Plate();
                    plate.setPlateName(entry.getKey()); // 分类名称作为plate名称

                    // 构建该分类下的短剧列表，去重相同的短剧
                    List<HomepageVo.PlateList> dramaList = new ArrayList<>(entry.getValue().stream()
                            .collect(Collectors.toMap(
                                    HomepageVoCategoryName::getMd5Id, // 使用md5Id作为key去重
                                    drama -> new HomepageVo.PlateList(
                                            drama.getMd5Id(),
                                            drama.getDramaId(),
                                            drama.getTitle(),
                                            drama.getViewsCount(),
                                            drama.getLikesCount(),
                                            drama.getThumbnailUrl()
                                    ),
                                    (existing, replacement) -> existing // 如果有重复，保留第一个
                            ))
                            .values());



                    plate.setPlateList(dramaList);
                    log.info("分类 {} 包含 {} 部短剧", entry.getKey(), dramaList.size());
                    return plate;
                })
                .collect(Collectors.toList());

        if (Objects.equals(req.getSiteDomain(), "bingelet.com")) {
            // 正序
            plateList.sort(Comparator.comparing(HomepageVo.Plate::getPlateName));
        }else {
            // 倒序
            plateList.sort((o1, o2) -> o2.getPlateName().compareTo(o1.getPlateName()));
        }

        resp.setPlateList(plateList);
        log.info("首页数据构建完成，共{}个分类", plateList.size());
        return resp;
    }

    public EpisodesVo episodesInfo(EpisodesReq req) {
        // 再查询 5 条除了 req.md5Id 以外的短剧
        LambdaQueryWrapper<ShortDramas> queryWrapper = new LambdaQueryWrapper<>();

        if (req.getMd5Id() != null && req.getDramaId() == null) {
//            先根据md5Id查询 dramaId
            queryWrapper.eq(ShortDramas::getMd5Id, req.getMd5Id());
            ShortDramas shortDramas = shortDramasMapper.selectOne(queryWrapper);
            if (shortDramas == null) {
                throw new BaseException(ErrorCode.FAILURE, "md5Id or dramaId is null");
            }
            queryWrapper.clear();
            req.setDramaId(String.valueOf(shortDramas.getDramaId()));
        }

        EpisodesVo resp =  shortDramasMapper.selectByDramaId(Integer.valueOf(req.getDramaId()));
        queryWrapper.ne(ShortDramas::getDramaId, Integer.valueOf(req.getDramaId()));

        queryWrapper.last("limit 6");
        List<ShortDramas> mayLikeList = shortDramasMapper.selectList(queryWrapper);
        resp.setMayLikeList(mayLikeList.stream().map(drama -> new HomepageVo.PlateList(
                drama.getMd5Id(),
                drama.getDramaId(),
                drama.getTitle(),
                drama.getViewsCount(),
                drama.getLikesCount(),
                drama.getThumbnailUrl()
        )).collect(Collectors.toList()));

        return resp;
    }

    public EpisodesDetailVo episodeDetailInfo(EpisodesDetailReq req , String token) {
        log.info("查询剧集详情请求: dramaId={}", req);
        EpisodesDetailVo resp = shortDramasMapper.selectDetailByDramaId(req.getDramaId(), req.getIndex());
        resp.setTitle(resp.getDramaTitle());
        log.info("剧集详情{}", resp);
        if (req.getIndex() > resp.getFreeCount() && resp.getIsVip() == 1) {
            // 判断是否为vip用户
            if (token == null) {
                throw new BaseException(ErrorCode.NOT_LOGIN, "not login");
            }
            UserTokenResp userTokenResp = userService.getUserBaseInfoFromToken(token);
            UserRespVo user = userService.getUser(userTokenResp.getUid());
            if (user == null) {
                throw new BaseException(ErrorCode.USER_NOT_EXIST, "user not exist");
            }
            LocalDateTime now = LocalDateTime.now();
            if (user.getUserStatus() == 2) {
                // 判断会员是否过期
                TUserAssetInfo assetInfo = TUserAssetInfoMapper.selectOne(new LambdaQueryWrapper<TUserAssetInfo>()
                        .eq(TUserAssetInfo::getUserId, userTokenResp.getUid())
                );
                if (assetInfo.getExpireTime().isBefore(now)) {
                    throw new BaseException(ErrorCode.USER_NOT_VIP, "vip user expired");
                }
                return resp;
            }
            // 如果不是会员 再判断有没有购买单部的记录
            TUserAssetInfo assetInfo = TUserAssetInfoMapper.selectOne(new LambdaQueryWrapper<TUserAssetInfo>()
                    .eq(TUserAssetInfo::getUserId, userTokenResp.getUid())
                    .eq(TUserAssetInfo::getDramaId, req.getDramaId())
                    .gt(TUserAssetInfo::getExpireTime, now)
                    .eq(TUserAssetInfo::getOrderType, 0)
            );
            if (assetInfo == null) {
                log.info("用户{}不是会员，且没有购买单部剧集{}的记录", userTokenResp.getUid(), req.getDramaId());
                throw new BaseException(ErrorCode.NOT_RECORD, "not buy this drama");
            }else {
                log.info("用户{}不是会员，但是购买了单部剧集{}的记录", userTokenResp.getUid(), req.getDramaId());
                return resp;
            }
        }
        return resp;
    }

    /**
     * 根据指定的分类ID列表获取短剧数据
     *
     * @param categoryIds 分类ID列表
     * @param siteDomain 站点域名
     * @return 按分类分组的短剧数据
     */
    public HomepageVo getShortDramasByCategoryIds(List<Integer> categoryIds, String siteDomain) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            throw new BaseException(ErrorCode.FAILURE, "categoryIds cannot be null or empty");
        }

        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(siteDomain);

        // 根据categoryIds查询drama_categories表，然后关联short_dramas_v1表获取剧的信息
        List<HomepageVoCategoryName> result = shortDramasMapper.homepageByCategoryIds(categoryIds);
        log.info("根据分类ID{}查询到{}条短剧-分类关联记录", categoryIds, result.size());

        // 按分类名称分组，每个分类包含其所有短剧
        Map<String, List<HomepageVoCategoryName>> groupedByCategory = result.stream()
                .collect(Collectors.groupingBy(HomepageVoCategoryName::getName));

        log.info("共有{}个分类", groupedByCategory.size());

        // 构建Plate列表
        List<HomepageVo.Plate> plateList = groupedByCategory.entrySet().stream()
                .map(entry -> {
                    HomepageVo.Plate plate = new HomepageVo.Plate();
                    plate.setPlateName(entry.getKey()); // 分类名称作为plate名称

                    // 构建该分类下的短剧列表，去重相同的短剧
                    List<HomepageVo.PlateList> dramaList = new ArrayList<>(entry.getValue().stream()
                            .collect(Collectors.toMap(
                                    HomepageVoCategoryName::getMd5Id, // 使用md5Id作为key去重
                                    drama -> new HomepageVo.PlateList(
                                            drama.getMd5Id(),
                                            drama.getDramaId(),
                                            drama.getTitle(),
                                            drama.getViewsCount(),
                                            drama.getLikesCount(),
                                            drama.getThumbnailUrl()
                                    ),
                                    (existing, replacement) -> existing // 如果有重复，保留第一个
                            ))
                            .values());

                    plate.setPlateList(dramaList);
                    log.info("分类 {} 包含 {} 部短剧", entry.getKey(), dramaList.size());
                    return plate;
                })
                .collect(Collectors.toList());

        resp.setPlateList(plateList);
        log.info("根据分类ID{}构建完成，共{}个分类", categoryIds, plateList.size());
        return resp;
    }
}
