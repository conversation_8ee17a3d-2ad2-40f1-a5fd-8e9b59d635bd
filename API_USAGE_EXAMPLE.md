# 根据分类ID获取短剧数据 API 使用示例

## 功能说明

新增了一个API接口，可以根据指定的分类ID列表查询drama_categories表中的所有相关数据，然后关联short_dramas_v1表获取剧的信息并返回。

## API 接口信息

- **接口路径**: `POST /api/v1/short-dramas/homepage/by-categories`
- **请求方式**: POST
- **Content-Type**: application/json

## 请求参数

```json
{
    "siteDomain": "vidfeedly.com",
    "categoryIds": [1, 2, 3, 4, 5, 6, 7, 8]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| siteDomain | String | 是 | 站点域名，默认为"vidfeedly.com" |
| categoryIds | List<Integer> | 是 | 分类ID列表，不能为空 |

## 响应示例

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "siteDomain": "vidfeedly.com",
        "plateList": [
            {
                "plateName": "爱情",
                "plateList": [
                    {
                        "md5Id": "abc123",
                        "dramaId": 1001,
                        "title": "霸道总裁爱上我",
                        "viewsCount": 50000,
                        "likesCount": 1200,
                        "thumbnailUrl": "https://example.com/thumbnail1.jpg"
                    },
                    {
                        "md5Id": "def456",
                        "dramaId": 1002,
                        "title": "甜蜜恋爱日记",
                        "viewsCount": 30000,
                        "likesCount": 800,
                        "thumbnailUrl": "https://example.com/thumbnail2.jpg"
                    }
                ]
            },
            {
                "plateName": "悬疑",
                "plateList": [
                    {
                        "md5Id": "ghi789",
                        "dramaId": 2001,
                        "title": "神秘案件",
                        "viewsCount": 40000,
                        "likesCount": 1000,
                        "thumbnailUrl": "https://example.com/thumbnail3.jpg"
                    }
                ]
            }
        ]
    }
}
```

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/api/v1/short-dramas/homepage/by-categories" \
  -H "Content-Type: application/json" \
  -d '{
    "siteDomain": "vidfeedly.com",
    "categoryIds": [1, 2, 3, 4, 5, 6, 7, 8]
  }'
```

### JavaScript 示例

```javascript
const response = await fetch('/api/v1/short-dramas/homepage/by-categories', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        siteDomain: 'vidfeedly.com',
        categoryIds: [1, 2, 3, 4, 5, 6, 7, 8]
    })
});

const data = await response.json();
console.log(data);
```

## 实现逻辑

1. **参数验证**: 验证siteDomain和categoryIds参数的有效性
2. **数据查询**: 根据categoryIds查询drama_categories表，关联short_dramas_v1表和categories表
3. **数据处理**: 按分类名称分组，去重相同的短剧
4. **结果排序**: 根据siteDomain决定分类的排序方式
5. **返回结果**: 返回按分类分组的短剧列表

## 错误处理

- 如果siteDomain为空，返回错误信息："siteDomain is null"
- 如果categoryIds为空或null，返回错误信息："categoryIds cannot be null or empty"

## 注意事项

1. 该接口会自动去重相同的短剧（基于md5Id）
2. 分类排序规则：
   - 如果siteDomain为"bingelet.com"，按分类名称正序排列
   - 其他情况按分类名称倒序排列
3. 只返回状态为1（已发布）的分类数据
