package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 短剧主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Getter
@Setter
@TableName("short_dramas_v1")
public class ShortDramas implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "drama_id", type = IdType.AUTO)
    private Integer dramaId;

    /**
     * 短剧标题
     */
    private String title;

    /**
     * 短剧剧情描述
     */
    private String description;

    /**
     * 国家/地区
     */
    private String country;

    /**
     * 短剧发布日期
     */
    private LocalDate releaseDate;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 点赞数
     */
    private Integer likesCount;

    /**
     * 浏览数
     */
    private Integer viewsCount;

    /**
     * 短剧封面图片链接
     */
    private String thumbnailUrl;

    /**
     * 短剧状态
     */
    private String status;

    /**
     * 来源ID
     */
    private Integer sourceId;

    /**
     * 热度(观看次数)
     */
    private Integer heat;

    /**
     * 收录日期
     */
    private LocalDateTime addedDate;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private String md5Id;

}
