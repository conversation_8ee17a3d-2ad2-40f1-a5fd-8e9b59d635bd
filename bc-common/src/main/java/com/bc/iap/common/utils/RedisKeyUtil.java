package com.bc.iap.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 管理reidis key
 */
public class RedisKeyUtil {
	public static final String SEPERATOR = ":";
	public static final String IMG_CODE_KEY = "img_code_uuid";
	public static final String LOGIN_TOKEN = "bc_login_token";
	public static final String LOGIN_TOKEN_UID = "bc_login_token_uid";
	public static final String LOGIN_TOKEN_APP_ID = "bc_login_token_app_id";
	public static final String USER_TOKEN = "bc_user_token";
	public static final String AC_TURNTABLE = "ac_turntable";
	public static final String AC_MAGIC_EGG = "ac_magic_egg";
	public static final String AC_SHOPPING = "ac_shopping";
	public static final String AC_DOWNLOAD_APP = "ac_download_app";
	public static final String AC_TURNTABLE_USED = "used";
	public static final String AC_UNLOCK = "ac_unlock";
	public static final String AC_OPENBOX = "ac_openbox";
	public static final String AC_EVENT = "ac_event";
	public static final String LOCK = "lock";
	public static final String FC_TRADE = "fc_trade";

	public static final String BLACK_LIST = "black";
	public static final String  COMPENSTAT = "compensate";
	public static final String  UK = "uk";

	public static final String USER_VALUE = "user_value";
	public static final String BLACK_LIST_IP_COUNTRY = BLACK_LIST + SEPERATOR + "country";

	public static String buildCompensateRef(Long appid, String uniqueId) {
		return StringUtils.joinWith(SEPERATOR, COMPENSTAT, appid, uniqueId);
	}

	public static String buildBlackListUserIpCountry(Long userId) {
		return BLACK_LIST_IP_COUNTRY + userId;
	}

	public static String buildUkRef(String appkey, String idType, String uniqueId) {
		return StringUtils.joinWith(SEPERATOR, UK, appkey, idType, uniqueId);
	}

	public static final String DUPLICATE_REQ = "dup";

	public static String buildDuplicateReq(String md5) {
		return DUPLICATE_REQ + SEPERATOR + md5;
	}

	/**
	 * 存放复活节集齐彩蛋用户数的key,类型为String,value为"集齐彩蛋用户数_最后更新时间"
	 */
	public static final String EASTER_EGG_PRIZE_USER = "sf_prize_user";

	/**
	 * 存放复活节用户是否首次进入H5游戏页面的key,格式为"easter_first:userId",类型为String,过期键,存在表示非首次进入,不存在表示首次进入
	 */
	public static final String EASTER_H5_GAME_FIRST_TIME = "easter_first";

	/**
	 * 存放复活节指定特殊字母已下发的个数的key,超过配置值则不下发该字母,格式为"easter_letter:字母:appId",类型为String,value为指定特殊字母已下发的个数
	 */
	public static final String EASTER_EGG_SPECIAL_LETTER_NUM = "easter_letter";

	/**
	 * 存放复活节已集齐彩蛋的用户的key,Set类型,value为已集齐彩蛋的用户id
	 */
	public static final String EASTER_EGG_FINISH_USER_NUM = "easter_finish";

	public static final String user_daily_asset = "daily:asset";

	public static final String user_daily_activity = "daily";

	public static String buildUserDailyActivity(String code, Long userId, LocalDateTime current) {
		return StringUtils.join(Arrays.asList(user_daily_activity, code, current.format(DateUtils.FMT_DAY_SHORT), userId), SEPERATOR);
	}

	public static String buildUserDailyAsset(Long userId, LocalDateTime current) {
		return StringUtils.join(Arrays.asList(user_daily_asset, userId, current.format(DateUtils.FMT_DAY_SHORT)), SEPERATOR);
	}

	public static final String AC_READ = "ac:read";

	/**
	 * 保存全局用户邀请码
	 */
	public static final String UC_INVITATION = "us:invitation";

	/**
	 * 邀请注册用户信息
	 * */
	public static final String INVITED_USER_INFO = "uc:invited:user";

	/**
	 * 用户参与活动事务锁
	 * */
	public static final String ACTIVITY_USER_KEY = "acu";

	/**
	 * 用户活动参与锁（tracking流程）
	 */
	public static final String ACTIVITY_USER_TRACING_KEY = "ac:tracing:";
	
	/**
	 * 金币提现事务锁
	 * */
	private static final String WITHDRAW_KEY = "withdraw";

	public static final String PAYPAL_OAUTH2_TOKEN = "paypal:oauth2token";

	public static final String BAK_PAYPAL_OAUTH2_TOKEN = "paypal:bakoauth2token";

	public static final String PAYPAL_OAUTH2_TOKEN_BY_ACCOUNT = "paypal:oauth2tokenbyaccount";

	public static final String BAK_PAYPAL_SWITCH = "paypal:bakswitch";

	public static final String PAYPAL_FUSE_ERROR = "paypal:fuseerror";
	public static final String BIGDATA_OFFER_CACHE = "bigdata:offercache";

	public static final String BAK_PAYPAL_CLOSE_AUTH = "paypal:bakcloseauth";

	public static final String BAK_PROXY_SWITCH = "proxy:bakswitch";
	public static final String PROXY_FUSE_ERROR = "proxy:fuseerror";

	public static final String BAK_PROXY_PAY_SWITCH = "proxy:pay:bakswitch";
	public static final String PROXY_PAY_FUSE_ERROR = "proxy:pay:fuseerror";

	public static final String REMITTANCERISE_CONTROL_FILTERRESULT = "remittancerisecontrol:filterresult";

	public static final String REMITTANCERISE_CONTROL_AUTH_FREE = "remittancerisecontrol:auth:free";

	public static final String REMITTANCERISE_CONTROL_AUTH_FAIL = "remittancerisecontrol:auth:fail";
	public static final String BRUSH_USER_CHECK = "brush:user:check";
	public static final String BRUSH_USER_BLACK = "brush:user:black";

	public static String buildRedisKey(String... keys) {
		StringBuilder keyName = new StringBuilder();
		for (String key : keys) {
			keyName.append(key).append(SEPERATOR);
		}
		return keyName.substring(0, keyName.length() - 1);

	}
	public static String buildWithdrawKey(Long userId) {
		return WITHDRAW_KEY + SEPERATOR + userId;
	}


	public static String buildWithdrawLimitKery(Long userId, Integer withdrawType) {
		return StringUtils.joinWith(SEPERATOR, WITHDRAW_KEY, userId, withdrawType);
	}


	public static String buildActivityUserKey(Integer aid, Long uid) {
		return StringUtils.join(ACTIVITY_USER_KEY, SEPERATOR, aid, SEPERATOR, uid);
	}

	/**
	 * 用户活动参与锁（tracking流程专用）
	 */
	public static String buildActivityUserTracingKey(Integer activityId, Long uid) {
		return StringUtils.join(ACTIVITY_USER_TRACING_KEY, SEPERATOR, activityId, SEPERATOR, uid);
	}

	public static String buildImgCodeKey(String uuid){
		return StringUtils.join(IMG_CODE_KEY, SEPERATOR, uuid);
	}

	public static String buildUserToken(Long uid){
		return StringUtils.join(USER_TOKEN, SEPERATOR, uid);
	}

	public static String buildLoginTokenKey(String token){
		return StringUtils.join(LOGIN_TOKEN, SEPERATOR, token);
	}

	public static String buildTurnTableUsedKey(Long uid){
		return StringUtils.join(AC_TURNTABLE, SEPERATOR,AC_TURNTABLE_USED,SEPERATOR, uid);
	}

	public static String buildMagicEggUsedKey(Long uid){
		return StringUtils.join(AC_MAGIC_EGG, SEPERATOR,LOCK,SEPERATOR, uid);
	}

	public static String buildShoppingUsedKey(Long uid){
		return StringUtils.join(AC_SHOPPING, SEPERATOR,LOCK,SEPERATOR, uid);
	}

	public static String buildOrderIdKey(Integer storeId, String orderId){
		return StringUtils.join(storeId,"#",orderId);
	}

	public static String buildUnLockLockKey(Long uid){
		return StringUtils.join(AC_UNLOCK, SEPERATOR,LOCK,SEPERATOR, uid);
	}

	public static String buildOpenBoxLockKey(Long uid){
		return StringUtils.join(AC_OPENBOX, SEPERATOR,LOCK,SEPERATOR, uid);
	}

	public static String buildEventLockKey(int acName, Long uid) {
		return StringUtils.join(AC_EVENT, SEPERATOR,LOCK,SEPERATOR,acName,SEPERATOR, uid);
	}
	
	public static String buildDownAppKey(Long uid,Long appId,String activityName,String pkgName){
		return StringUtils.join(AC_DOWNLOAD_APP, SEPERATOR,LOCK,SEPERATOR,uid,SEPERATOR, appId ,SEPERATOR,activityName,SEPERATOR,pkgName);

	}

	public static String buildReadKey(Long uid) {
		return StringUtils.join(AC_READ, SEPERATOR, uid);
	}
	
	public static String buildTradeKey(Long uid,Integer transactionType) {
		return StringUtils.join(FC_TRADE, SEPERATOR, uid,SEPERATOR,transactionType);
	}
	
	public static String buildTradeKey(Long uid,Integer transactionType,String transactionId) {
		return StringUtils.join(FC_TRADE, SEPERATOR, uid,SEPERATOR,transactionType,SEPERATOR, transactionId);
	}

	public static String buildEasterGameKey(Long userId){
		return StringUtils.join(EASTER_H5_GAME_FIRST_TIME,SEPERATOR,userId);
	}

	public static String buildEasterLetterLimitKey(String letter,Long appId){
		return StringUtils.join(EASTER_EGG_SPECIAL_LETTER_NUM,SEPERATOR,letter,SEPERATOR,appId);
	}

	public static String buildInvitatedUserInfoKey(Long userId) {
		return INVITED_USER_INFO + SEPERATOR + userId;
	}

	public static String buildPayPalOauth2TokenKey() {
		return PAYPAL_OAUTH2_TOKEN;
	}
	public static String buildBakPayPalOauth2TokenKey() {
		return BAK_PAYPAL_OAUTH2_TOKEN;
	}

	public static String buildPayPalOauth2TokenKeyByAccount(String accountName) {
		return PAYPAL_OAUTH2_TOKEN_BY_ACCOUNT + SEPERATOR + accountName;
	}

	public static String buildBakPayPalSwitchKey() {
		return BAK_PAYPAL_SWITCH;
	}
	public static String buildPayPalFuseErrorKey() {
		return PAYPAL_FUSE_ERROR;
	}
	public static String buildCLoseBakPayPalAuth(String auth) {
		return BAK_PAYPAL_CLOSE_AUTH+ SEPERATOR + auth;
	}
	public static String buildBigdataOfferCacheKey() {
		return BIGDATA_OFFER_CACHE;
	}


	public static String buildRemittanceFilterResult(String flag) {
		return REMITTANCERISE_CONTROL_FILTERRESULT+ SEPERATOR + flag;
	}
	public static String buildRemittanceRiseControlFreeAuthKey(String auth) {
		return REMITTANCERISE_CONTROL_AUTH_FREE+ SEPERATOR + auth;
	}
	public static String buildRemittanceRiseControlFailAuthKey(String auth) {
		return REMITTANCERISE_CONTROL_AUTH_FAIL+ SEPERATOR + auth;
	}

	public static final String TASK_ODID = "task:odid";

	/**
	 * 删除僵尸用户数据任务队列
	 * sorted set 结构, 以 用户最后登录时间为分数, value为 userId
	 * */
	public static final String TASK_DEL_ZOMBIES = "task:del:zombies";

	public static String buildBakProxySwitchKey() {
		return BAK_PROXY_SWITCH;
	}

	public static String buildProxyFuseErrorKey() {
		return PROXY_FUSE_ERROR;
	}

	public static String buildBakProxyPaySwitchKey() {
		return BAK_PROXY_PAY_SWITCH;
	}

	public static String buildProxyPayFuseErrorKey() {
		return PROXY_PAY_FUSE_ERROR;
	}

	public static final String PROXY_PAY_CREATE_ORDER_ERROR = "proxy:pay:error:order";

	public static String buildProxyPayFailRecordKey(String proxyPayType, String country) {
		return PROXY_PAY_CREATE_ORDER_ERROR + SEPERATOR + proxyPayType + SEPERATOR + country;
	}

	public static final String PROXY_PAY_CREATE_ORDER_ERROR_RECORD = "proxy:pay:error:order:flag";

	public static String buildProxyPayFailFlagKey(String proxyPayType, String country) {
		return PROXY_PAY_CREATE_ORDER_ERROR_RECORD + SEPERATOR + proxyPayType + SEPERATOR + country;
	}

	public static final String PROXY_PAY_DAILY_QUOTA_RECORD = "proxy:pay:error:daily_quota:flag";

	public static String buildProxyPayDailyQuotaFlagKey(String proxyPayType) {
		return PROXY_PAY_CREATE_ORDER_ERROR_RECORD + SEPERATOR + proxyPayType;
	}

	public static final String PROXY_PAY_IFRAME_DENY = "proxy:pay:platform:iframe:deny";

	public static String buildProxyPayPlatformIframeDenyKey(String proxyPayType) {
		return PROXY_PAY_IFRAME_DENY + SEPERATOR + proxyPayType;
	}

	public static final String PROXY_PAY_CACHE_ORDER = "proxy:pay:cache:order:";

	public static String buildProxyPayOrderCache(String googleId, String sid, String amount) {
		return PROXY_PAY_CACHE_ORDER + googleId + SEPERATOR + sid + SEPERATOR + amount;
	}


	public static final String PROXY_PAY_TYPE_CACHE_ORDER = "proxy:pay:cache:order:type:";

	public static String buildProxyPayOrderTypeCache(String googleId, String sid, String amount) {
		return PROXY_PAY_TYPE_CACHE_ORDER + googleId + SEPERATOR + sid + SEPERATOR + amount;
	}

	public static final String PROXY_PAY_CONFIRM_HASH_CACHE_ORDER = "proxy:pay:cache:order:confirm:hash:";

	public static String buildProxyPayConfirmOrderHashKey(String googleId, String sid) {
		return PROXY_PAY_CONFIRM_HASH_CACHE_ORDER + googleId + SEPERATOR + sid;
	}

	/**
	 * 僵尸用户超10w需要自动清除的app
	 */
	public static final String AUTO_DEL_APP = "auto_del_app";

}
