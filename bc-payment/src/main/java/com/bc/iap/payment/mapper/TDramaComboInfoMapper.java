package com.bc.iap.payment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.payment.model.entity.TDramaComboInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 用户套餐信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface TDramaComboInfoMapper extends BaseMapper<TDramaComboInfo> {


    @Select("select * from t_drama_combo_info where (app_id in (#{appId},0)) and combo_id=#{comboId}")
    List<TDramaComboInfo> selectComboInfo(@Param("appId")Long appId,
                                          @Param("comboId")Long comboId);
}
