package com.bc.iap.payment.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.bc.iap.common.dto.exception.BaseException;
import com.bc.iap.common.utils.DateUtils;
import com.bc.iap.common.utils.IpAddressUtils;
import com.bc.iap.common.utils.SnowFlake;
import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentStatusEnum;
import com.bc.iap.payment.enums.PaymentSubTypeEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.mapper.TDramaComboInfoMapper;
import com.bc.iap.payment.mapper.TDramaOrderMapper;
import com.bc.iap.payment.model.dto.*;
import com.bc.iap.payment.model.entity.TDramaComboInfo;
import com.bc.iap.payment.model.entity.TDramaOrder;
import com.bc.iap.payment.model.req.PaymentBaseReq;
import com.bc.iap.payment.model.resp.PaymentRspDto;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.enums.ComboTypeEnum;
import com.bc.iap.user.utils.UserInfoThreadLocal;
import com.zz.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.bc.iap.payment.model.dto.PaymentSubscriptionDto.*;

@Component
@RequiredArgsConstructor
@Slf4j
public class ComplexPaymentService {

    private final Environment environment;

    private final TDramaComboInfoMapper dramaComboInfoMapper;

    private final TDramaOrderMapper dramaOrderMapper;

    private final List<PaymentService> paymentServiceList;

    private final AssetService assetService;


    private final Map<PaymentTypeEnum, PaymentService> paymentServiceMap = new HashMap<>();


    /**
     * Init.
     */
    @PostConstruct
    public void init() {
        paymentServiceList.forEach(x -> {
            paymentServiceMap.put(x.getPaymentType(), x);
        });
    }

    /**
     * 创建订单
     *
     * @param request
     * @param commonReq
     * @return
     */
    public PaymentRspVo createPaymentOrder(HttpServletRequest request, PaymentBaseReq commonReq) {
        PaymentInfoDto paymentInfoDto = buildPayInfo(request, commonReq);
        assertTrue(paymentInfoDto.getProxyPayTypeEnum() != null, paymentInfoDto, "Incorrect payment method selection");
        // 套餐是剧集需要传剧集ID
        if (paymentInfoDto.getOrderType() == ComboTypeEnum.EPISODE.getType()) {
            assertTrue(commonReq.getDramaId() != null && commonReq.getDramaId() > 0, paymentInfoDto, "剧集支付,剧集id为空");
        }
        TDramaOrder orderInfo = dramaOrderMapper.queryOrderByUidAndCid(paymentInfoDto.getUid(), paymentInfoDto.getDramaId());
        boolean isUpdate = true;
        if (orderInfo == null || orderInfo.getPayStatus() != PaymentStatusEnum.WAIT_PAY.getStatus()) {
            orderInfo = buildDramaOrderInfo(paymentInfoDto, null);
            isUpdate = false;
        } else {
            paymentInfoDto.setOrderId(orderInfo.getOrderId());
        }
        Optional<PaymentService> paymentServiceOptional = paymentServiceList.stream().filter(x -> x.getPaymentType() == paymentInfoDto.getProxyPayTypeEnum()).findFirst();
        assertTrue(paymentServiceOptional.isPresent(), paymentInfoDto, "没有合适的付款方式");
        PaymentService paymentService = paymentServiceOptional.get();
        PaymentRspDto paymentRspDto = paymentService.launchPayment(paymentInfoDto);
        assertTrue(paymentRspDto != null && Strings.isNotBlank(paymentRspDto.getRedirectUrl()), paymentInfoDto, "order created error");
        PaymentRspVo paymentRspVo = new PaymentRspVo();
        processRspVo(paymentInfoDto, paymentRspDto, paymentRspVo);
        updateOrCreateOrder(buildDramaOrderInfo(paymentInfoDto, paymentRspDto), false);
        return paymentRspVo;
    }

    public Map<String, String> getPaymentSupportType(HttpServletRequest request, PaymentBaseReq baseReq) {
        return new HashMap<>();
    }


    private void updateOrCreateOrder(TDramaOrder orderInfo, boolean isUpdate) {
        if (isUpdate) {
            dramaOrderMapper.updateById(orderInfo);
        } else {
            dramaOrderMapper.insert(orderInfo);
        }
    }

    /**
     * 处理vo响应
     *
     * @param payRspDto
     * @param sdkRspVo
     */
    private void processRspVo(PaymentInfoDto paymentInfoDto, PaymentRspDto payRspDto, PaymentRspVo sdkRspVo) {
        sdkRspVo.setRedirectUrl(payRspDto.getRedirectUrl());
        sdkRspVo.setOrderId(paymentInfoDto.getOrderId());
        sdkRspVo.setStatus(payRspDto.getStatus());
        sdkRspVo.setMessage(payRspDto.getMessage());
    }

    public PaymentInfoDto buildPayInfo(HttpServletRequest request, PaymentBaseReq commonReq) {
        UserRespVo userRespVo = UserInfoThreadLocal.getUser();
        PaymentInfoDto paymentInfoDto = new PaymentInfoDto();
        String ip = IpAddressUtils.getClientIpAddr(request);
        BeanUtils.copyProperties(commonReq, paymentInfoDto);
        paymentInfoDto.setAppId(userRespVo.getAppId());
        paymentInfoDto.setUid(userRespVo.getUserId());
        paymentInfoDto.setIp(ip);
//        String country = environment.getActiveProfiles()[0].equals("test") ?
//                "US" : IPUtils.getCountryCode(ip);
        //默认美国
        String country = "US";
        //解析不到或为unknow转us
        country = Strings.isBlank(country) || "unknow".equalsIgnoreCase(country) ? "US" : country;
        country = "GB".equalsIgnoreCase(country) ? "UK" : country;
        CountryEnum countryEnum = CountryEnum.getCountryEnum(country);
        assertTrue(countryEnum != null, paymentInfoDto, "Unsupported countries");
        paymentInfoDto.setCountryEnum(countryEnum);
        List<TDramaComboInfo> comboInfoList = dramaComboInfoMapper.selectComboInfo(userRespVo.getAppId(), commonReq.getComboId());
        TDramaComboInfo comboInfo = comboInfoList.get(0);
        paymentInfoDto.setOrderType(comboInfo.getComboType());
        paymentInfoDto.setAmount(comboInfo.getSellingPrice());
        paymentInfoDto.setOrderId(String.valueOf(SnowFlake.getInstance().nextId()));
        paymentInfoDto.setSuccessUrl(commonReq.getSyncUrl());
        paymentInfoDto.setProxyPayTypeEnum(PaymentTypeEnum.PAYER_MAX);
        paymentInfoDto.setSubTypeEnum(PaymentSubTypeEnum.CARD);
        if (comboInfo.getPaymentType() == 1) {
            PaymentSubscriptionDto subscriptionDto = new PaymentSubscriptionDto();
            subscriptionDto.setSubject("subscription member");
            subscriptionDto.setDescription("payment");
            subscriptionDto.setTotalPeriods(comboInfo.getTotalPeriods());
            PeriodRuleDTO periodRuleDTO = new PeriodRuleDTO();
            periodRuleDTO.setPeriodUnit(ComboTypeEnum.getComboType(comboInfo.getComboType()).getUnit());
            periodRuleDTO.setPeriodCount(1);
            subscriptionDto.setPeriodRule(periodRuleDTO);
            PeriodAmountDTO periodAmountDTO = new PeriodAmountDTO();
            periodAmountDTO.setAmount(comboInfo.getSellingPrice());
            periodAmountDTO.setCurrency(countryEnum.getCurrencyCode());
            subscriptionDto.setPeriodAmount(periodAmountDTO);
            if (!Objects.equals(comboInfo.getDiscountPrice(), comboInfo.getSellingPrice())) {
                TrialPeriodConfigDTO periodConfigDTO = new TrialPeriodConfigDTO();
                periodConfigDTO.setTrialPeriodCount(1);
                TrialPeriodAmountDTO trialPeriodAmountDTO = new TrialPeriodAmountDTO();
                trialPeriodAmountDTO.setAmount(comboInfo.getDiscountPrice());
                trialPeriodAmountDTO.setCurrency(countryEnum.getCurrencyCode());
                periodConfigDTO.setTrialPeriodAmountDTO(trialPeriodAmountDTO);
                subscriptionDto.setTrialPeriodConfig(periodConfigDTO);
            }
            //第一次扣款30分钟后
            subscriptionDto.setFirstPeriodStartDate(ZonedDateTime.now().plusMinutes(30).format(DateUtils.FMT_RFC_3339));
            paymentInfoDto.setSubscriptionDto(subscriptionDto);
        }
        return paymentInfoDto;
    }

    public void assertTrue(boolean expression, PaymentInfoDto infoDto, String message) {
        if (!expression) {
//            Map<String, Object> eventInfo = fillEventInfo(infoDto.getProxyPayTypeEnum() == null ? "" : infoDto.getProxyPayTypeEnum().getType(),
//                    infoDto.getAmount() == null ? "" : infoDto.getAmount().toPlainString(),
//                    PaymentConstant.FAIL_CODE, message,
//                    infoDto.getSubTypeEnum() == null ? "" : infoDto.getSubTypeEnum().getSubType(),
//                    infoDto.getProxyPayVer(), infoDto.getStatDto());
//            sendOrderEventInfoV2(ProxyPayEventEnum.ORDER_BUILD, infoDto, null, eventInfo);
            throw new BaseException(-1, message);
        }
    }


    private TDramaOrder buildDramaOrderInfo(PaymentInfoDto payInfoDto, PaymentRspDto rspDto) {
        TDramaOrder orderInfo = new TDramaOrder();
        orderInfo.setOrderId(payInfoDto.getOrderId());
        orderInfo.setOrderType(payInfoDto.getOrderType());
        orderInfo.setAppId(payInfoDto.getAppId());
        orderInfo.setUserId(payInfoDto.getUid());
        orderInfo.setDramaId(payInfoDto.getDramaId());
        orderInfo.setIp(payInfoDto.getIp());
        orderInfo.setCountry(payInfoDto.getCountryEnum().getCountryCode());
        orderInfo.setCurrency(payInfoDto.getCountryEnum().getCurrencyCode());
        orderInfo.setAmount(payInfoDto.getAmount());
        orderInfo.setUsdValue(payInfoDto.getAmount());
//        if (!CountryEnum.US.getCurrencyCode().equalsIgnoreCase(payInfoDto.getCountryEnum().getCurrencyCode())) {
//            BigDecimal exchangeRate = exchangeRateService.getDollarExchangeRate(payInfoDto.getCountryEnum().getCurrencyCode());
//            BigDecimal usdValue = payInfoDto.getAmount().divide(exchangeRate, 6, RoundingMode.HALF_DOWN);
//            orderInfo.setUsdValue(usdValue);
//        }
        orderInfo.setPayStatus(PaymentStatusEnum.WAIT_PAY.getStatus());
        orderInfo.setRemarks(payInfoDto.getProxyPayVer());
        if (payInfoDto.getProxyPayTypeEnum() != null) {
            orderInfo.setPayPlatform(payInfoDto.getProxyPayTypeEnum().getChannel());
        }
        if (payInfoDto.getSubTypeEnum() != null) {
            orderInfo.setPaymentMethodType(payInfoDto.getSubTypeEnum().getSubType().toLowerCase());
        }
        if (rspDto != null) {
            orderInfo.setMerchantId(rspDto.getMerchantId());
            orderInfo.setOutTradeNo(rspDto.getOutTradeNo());
        }
        orderInfo.setPaymentStatInfo(JSON.toJSONString(payInfoDto.getStatDto()));
        return orderInfo;
    }


    /**
     * 客户端代付状态请求
     *
     * @param request
     * @param commonReq
     * @return
     */
    public PaymentRspVo syncPaymentStatusReq(HttpServletRequest request, PaymentBaseReq commonReq) {
        UserRespVo userRespVo = UserInfoThreadLocal.getUser();
        TDramaOrder orderInfo = dramaOrderMapper.queryOrderByUid(userRespVo.getUserId());
        assertTrue(orderInfo != null, null, "order not exists");
        int status = orderInfo.getPayStatus();
        if (orderInfo.getPayStatus() == PaymentStatusEnum.WAIT_PAY.getStatus()) {
            PaymentTypeEnum typeEnum = PaymentTypeEnum.getChannel(orderInfo.getPayPlatform());
            PaymentService proxyPayService = paymentServiceMap.get(typeEnum);
            QueryOrderStatusDto statusDto = new QueryOrderStatusDto();
            statusDto.setMerchantId(orderInfo.getMerchantId());
            statusDto.setOrderId(orderInfo.getOrderId());
            statusDto.setOutTradeNo(orderInfo.getOutTradeNo());
            PaymentOrderStatusInfoDto statusInfoDto = proxyPayService.queryOrderStatus(statusDto);
            if (statusInfoDto == null) {
                PaymentRspVo proxyPayRspVo = new PaymentRspVo();
                proxyPayRspVo.setOrderId(orderInfo.getOrderId());
                proxyPayRspVo.setStatus(String.valueOf(PaymentStatusEnum.WAIT_PAY.getStatus()));
                return proxyPayRspVo;
            }
            orderStatusUpdate(orderInfo, statusInfoDto);
            status = statusInfoDto.getStatusEnum().getStatus();
        }
        PaymentRspVo proxyPayRspVo = new PaymentRspVo();
        proxyPayRspVo.setOrderId(orderInfo.getOrderId());
        proxyPayRspVo.setStatus(String.valueOf(status));
        return proxyPayRspVo;
    }

    /**
     * 订单状态更新
     *
     * @param fcPayOrderInfo
     * @param statusInfoDto
     */
    private void orderStatusUpdate(TDramaOrder fcPayOrderInfo, PaymentOrderStatusInfoDto statusInfoDto) {
        fcPayOrderInfo.setPayStatus(statusInfoDto.getStatusEnum().getStatus());
        if (Strings.isNotBlank(statusInfoDto.getMessage())) {
            fcPayOrderInfo.setOrderMessage(statusInfoDto.getMessage().length() > 200 ? statusInfoDto.getMessage().substring(0, 200) : statusInfoDto.getMessage());
        }
        if (statusInfoDto.getStatusEnum() == PaymentStatusEnum.SUCCESS_PAY) {
            fcPayOrderInfo.setRealAmount(statusInfoDto.getAmount());
            BigDecimal usdValue = fcPayOrderInfo.getRealAmount();
//            if (Strings.isNotBlank(fcPayOrderInfo.getCurrency()) && !CountryEnum.US.getCurrencyCode().equalsIgnoreCase(fcPayOrderInfo.getCurrency())) {
//                BigDecimal exchangeRate = exchangeRateService.getDollarExchangeRate(fcPayOrderInfo.getCurrency());
//                usdValue = usdValue.divide(exchangeRate, 6, RoundingMode.HALF_DOWN);
//            }
            fcPayOrderInfo.setUsdValue(usdValue);
            fcPayOrderInfo.setPaymentOtherInfo(statusInfoDto.getPaymentOtherInfo());
            fcPayOrderInfo.setPaymentMethodType(Optional.ofNullable(statusInfoDto.getPaymentMethodType()).orElse("").toLowerCase());
            //更新用户资产
            assetService.updateUserAssetInfo(fcPayOrderInfo);
        }
        fcPayOrderInfo.setOrderFinishTime(statusInfoDto.getFinishTime());
        UpdateWrapper<TDramaOrder> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("order_id", fcPayOrderInfo.getOrderId());
        int count = dramaOrderMapper.update(fcPayOrderInfo, updateWrapper);
    }


    /**
     * 获取订单列表
     *
     * @return
     */
    public PaymentOrderRspVo getOrderList() {
        UserRespVo userRespVo = UserInfoThreadLocal.getUser();
        List<TDramaOrder> tFcPayOrderInfos = dramaOrderMapper.selectOrderByUidAndAid(userRespVo.getUserId(), userRespVo.getAppId());
        if (CollectionUtils.isNotEmpty(tFcPayOrderInfos)) {
            List<PaymentOrderRspVo.OrderDataDto> orderDataDtos = tFcPayOrderInfos.stream().map(x -> {
                PaymentOrderRspVo.OrderDataDto orderDataDto = new PaymentOrderRspVo.OrderDataDto();
                orderDataDto.setOrderId(x.getOrderId());
                orderDataDto.setDramaId(x.getDramaId());
                orderDataDto.setCurrency(x.getCurrency());
                orderDataDto.setOrderType(x.getOrderType());
                orderDataDto.setAmount(x.getAmount());
                orderDataDto.setStatus(x.getPayStatus());
                orderDataDto.setFinishTime(x.getOrderFinishTime());
                return orderDataDto;
            }).collect(Collectors.toList());
            return new PaymentOrderRspVo(orderDataDtos);
        }
        return null;
    }


    /**
     * 支付通知回调处理
     *
     * @param paramMap
     * @param paymentTypeEnum
     * @param request
     */
    public PaymentCallbackRspVo notifyCallbackProcess(Map<String, Object> paramMap, PaymentTypeEnum paymentTypeEnum, HttpServletRequest request) {
        PaymentCallbackInfoDto callbackInfoDto = paymentServiceMap.get(paymentTypeEnum).paymentCallback(paramMap, request);
        if (callbackInfoDto == null) {
            log.warn("代付回调信息解析失败，请检查是否格式有误!json:{}", JSON.toJSONString(paramMap));
            return null;
        }
        if (callbackInfoDto.getStatusEnum() == PaymentStatusEnum.SKIP_PROCESS) {
            return null;
        }
        TDramaOrder tFcPayOrderInfo = null;
        if (Strings.isNotBlank(callbackInfoDto.getOrderId())) {
            tFcPayOrderInfo = dramaOrderMapper.queryOrderByOrderId(callbackInfoDto.getOrderId());
        }
        if (Strings.isNotBlank(callbackInfoDto.getOrderTradeNo())) {
            tFcPayOrderInfo = dramaOrderMapper.queryOrderByTradeId(callbackInfoDto.getOrderTradeNo());
        }
        //没订单或已经有平台回调成功了，不处理
        if (tFcPayOrderInfo == null || tFcPayOrderInfo.getPayStatus() == PaymentStatusEnum.SUCCESS_PAY.getStatus()) {
            return null;
        }
        if (Strings.isNotBlank(callbackInfoDto.getTransactionType())) {
            tFcPayOrderInfo.setPaymentMethodType(callbackInfoDto.getTransactionType().toLowerCase());
        }
        tFcPayOrderInfo.setOrderMessage(callbackInfoDto.getMessage());
        if (callbackInfoDto.getStatusEnum() == PaymentStatusEnum.SUCCESS_PAY) {
            tFcPayOrderInfo.setRealAmount(callbackInfoDto.getRealAmount());
            BigDecimal usdValue = tFcPayOrderInfo.getRealAmount();
//            if (Strings.isNotBlank(tFcPayOrderInfo.getCurrency()) && !CountryEnum.US.getCurrencyCode().equalsIgnoreCase(tFcPayOrderInfo.getCurrency())) {
//                BigDecimal exchangeRate = exchangeRateService.getDollarExchangeRate(tFcPayOrderInfo.getCurrency());
//                usdValue = usdValue.divide(exchangeRate, 6, RoundingMode.HALF_DOWN);
//            }
            tFcPayOrderInfo.setUsdValue(usdValue);
            tFcPayOrderInfo.setPaymentOtherInfo(callbackInfoDto.getPaymentOtherInfo());
//            Map<String, Object> eventInfo = fillEventInfo(paymentTypeEnum.getType(),
//                    tFcPayOrderInfo.getRealAmount().toPlainString(), tFcPayOrderInfo.getOrderId(), callbackInfoDto.getTransactionType(),
//                    tFcPayOrderInfo.getPaymentMethodType(), tFcPayOrderInfo.getRemarks(),
//                    JSON.parseObject(tFcPayOrderInfo.getPaymentStatInfo(), ProxyPayStatDto.class));
//            sendOrderEventInfoV2(ProxyPayEventEnum.ORDER_PAY_SUC, null, tFcPayOrderInfo, eventInfo);
            //更新用户资产
            assetService.updateUserAssetInfo(tFcPayOrderInfo);
        } else {
//            Map<String, Object> eventInfo = fillEventInfo(paymentTypeEnum.getType(),
//                    tFcPayOrderInfo.getAmount().toPlainString(), tFcPayOrderInfo.getOrderId(), callbackInfoDto.getMessage(),
//                    tFcPayOrderInfo.getPaymentMethodType(), tFcPayOrderInfo.getRemarks(),
//                    JSON.parseObject(tFcPayOrderInfo.getPaymentStatInfo(), ProxyPayStatDto.class));
//            sendOrderEventInfoV2(ProxyPayEventEnum.ORDER_PAY_FAIL, null, tFcPayOrderInfo, eventInfo);
        }
        tFcPayOrderInfo.setMerFee(callbackInfoDto.getMerFee());
        tFcPayOrderInfo.setMerFeeCurrency(callbackInfoDto.getMerFeeCurrency());
        tFcPayOrderInfo.setPayStatus(callbackInfoDto.getStatusEnum().getStatus());
        if (callbackInfoDto.getFinishTime() != null) {
            tFcPayOrderInfo.setOrderFinishTime(callbackInfoDto.getFinishTime());
        }
        dramaOrderMapper.updateOrderInfo(tFcPayOrderInfo);
        PaymentCallbackRspVo callbackRspVo = new PaymentCallbackRspVo();
        callbackRspVo.setStatus(callbackInfoDto.getStatus());
        callbackRspVo.setTxnId(callbackInfoDto.getOrderTradeNo());
        return callbackRspVo;
    }

    public void checkProxyPayOrderStatus() {
        //查询60分钟前到当前时间-5分钟未完成订单
        String beginDt = DateUtils.format(DateUtils.addMins(new Date(), -60), DateUtils.DATE_TIME_PATTERN);
        String endDate = DateUtils.format(DateUtils.addMins(new Date(), -5), DateUtils.DATE_TIME_PATTERN);
        List<TDramaOrder> waitPayOrderList = dramaOrderMapper.queryOrderByStatus(PaymentStatusEnum.WAIT_PAY.getStatus(), beginDt, endDate);
        if (CollectionUtils.isEmpty(waitPayOrderList)) {
            return;
        }
        for (TDramaOrder fcPayOrderInfo : waitPayOrderList) {
            PaymentTypeEnum typeEnum = PaymentTypeEnum.getChannel(fcPayOrderInfo.getPayPlatform());
            PaymentService proxyPayService = paymentServiceMap.get(typeEnum);
            if (proxyPayService == null) {
                continue;
            }
            QueryOrderStatusDto queryOrderDto = new QueryOrderStatusDto(fcPayOrderInfo.getMerchantId(), fcPayOrderInfo.getOrderId(), fcPayOrderInfo.getOutTradeNo());
            PaymentOrderStatusInfoDto statusInfoDto = proxyPayService.queryOrderStatus(queryOrderDto);
            if (statusInfoDto == null) {
                continue;
            }
            orderStatusUpdate(fcPayOrderInfo, statusInfoDto);
            log.info("订单状态查询,orderId:{},status:{}", fcPayOrderInfo.getOrderId(), statusInfoDto.getStatusEnum().getRemarks());
        }
    }
}


