package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.web.common.dto.CategoryHomepageReq;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.HomepageReq;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVo;
import com.bc.iap.web.service.IShortDramasService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短剧相关接口控制器
 *
 * 提供短剧首页数据、剧集信息、剧集详情等功能
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/short-dramas")
public class ShortDramasController {
    private final IShortDramasService service;

    /**
     * 获取首页数据
     *
     * 返回按分类分组的短剧列表，用于首页展示
     *
     * @param req 首页请求参数，包含站点域名等信息
     * @return 包含分类短剧列表的首页数据
     */
    @PostMapping("/homepage")
    public BaseResponse<HomepageVo> getHomepage(@Valid @RequestBody HomepageReq req) {
        log.info("获取首页数据请求: {}", req);
//        HomepageVo result = service.homepage(req);
//        log.info("首页数据获取成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
//        return BaseResponse.success(result);
        Map<String, List<Integer>> siteCategory = new HashMap<>();

        siteCategory.put("vidfeedly.com", Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8));
        siteCategory.put("bingelet.com", Arrays.asList(8, 7, 6, 5, 4, 3, 2, 1));
        if (siteCategory.get(req.getSiteDomain()) == null) {
            throw new RuntimeException("siteDomain is not exist");
        }
        HomepageVo result = service.getShortDramasByCategoryIds(siteCategory.get(req.getSiteDomain()), req.getSiteDomain());
        log.info("根据分类ID获取首页数据成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
        return BaseResponse.success(result);
    }

    /**
     * 根据指定分类ID获取首页数据
     *
     * 根据传入的分类ID列表，查询drama_categories表中的所有相关数据，
     * 然后关联short_dramas_v1表获取剧的信息并返回
     *
     * @param req 包含分类ID列表和站点域名的请求参数
     * @return 包含指定分类短剧列表的首页数据
     */
    @PostMapping("/homepage/by-categories")
    public BaseResponse<HomepageVo> getHomepageByCategoryIds(@Valid @RequestBody CategoryHomepageReq req) {
        log.info("根据分类ID获取首页数据请求: categoryIds={}, siteDomain={}", req.getCategoryIds(), req.getSiteDomain());
        HomepageVo result = service.getShortDramasByCategoryIds(req.getCategoryIds(), req.getSiteDomain());
        log.info("根据分类ID获取首页数据成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
        return BaseResponse.success(result);
    }

    /**
     * 根据短剧MD5ID 或者 dramaId 查询剧集信息
     *
     * 获取指定短剧的基本信息和剧集总数
     *
     * @param req 包含短剧MD5ID的请求参数
     * @return 短剧的剧集基本信息（标题、总集数等）
     */
    @PostMapping("/episodes")
    public BaseResponse<EpisodesVo> getEpisodes(@Valid @RequestBody EpisodesReq req) {
        log.info("查询剧集信息请求: md5Id={}", req.getMd5Id());
        EpisodesVo result = service.episodesInfo(req);
        log.info("剧集信息查询成功: title={}, totalCount={}", result.getTitle(), result.getTotalCount());
        return BaseResponse.success(result);
    }

    /**
     * 根据剧集ID和集数序号查询剧集详情
     *
     * 获取指定剧集的详细信息，包括视频链接、时长等
     *
     * @param req 包含剧集ID和集数序号的请求参数
     * @return 剧集详细信息（视频链接、时长、观看次数等）
     */
    @PostMapping("/detail")
    public BaseResponse<?> getEpisodeDetail(@Valid @RequestBody EpisodesDetailReq req, @RequestHeader(value = "Authorization", required = false) String token) {
        log.info("查询剧集详情请求: dramaId={}, index={}", req.getDramaId(), req.getIndex());
        EpisodesDetailVo result = service.episodeDetailInfo(req, token);
        log.info("剧集详情查询成功: episodeId={}, title={}", result.getEpisodeId(), result.getTitle());
        if (result.getMd5Id() == null) {
            return BaseResponse.fail(ErrorCode.FAILURE);
        }
        return BaseResponse.success(result);
    }
}
