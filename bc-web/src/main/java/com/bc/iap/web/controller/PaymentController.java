package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.model.dto.PaymentCallbackRspVo;
import com.bc.iap.payment.model.req.PaymentBaseReq;
import com.bc.iap.payment.service.ComplexPaymentService;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@RequiredArgsConstructor
@Controller
@RequestMapping("/api/v1/payment")
@Slf4j
@Validated
public class PaymentController {

    private final ComplexPaymentService complexPaymentService;


    @RequestMapping("/payType")
    @ResponseBody
    public BaseResponse<?> getPaymentType(@RequestBody PaymentBaseReq baseReq, HttpServletRequest request) {
        return BaseResponse.success(complexPaymentService.getPaymentSupportType(request, baseReq));
    }

    @RequestMapping("/orderPay")
    @ResponseBody
    public BaseResponse<?> payment(@RequestBody PaymentBaseReq baseReq, HttpServletRequest request) {
        return BaseResponse.success(complexPaymentService.createPaymentOrder(request, baseReq));
    }

//    @RequestMapping("/payment/confirm")
//    @ResponseBody
//    public BaseResponse<?> PaymentConfirm(@RequestBody PaymentBaseReq baseReq, HttpServletRequest request) {
//        return BaseResponse.success(complexPaymentService.confirmOrder(request, baseReq));
//    }

    @RequestMapping("/order/status")
    @ResponseBody
    public BaseResponse<?> paymentOrderStatus(@RequestBody PaymentBaseReq baseReq, HttpServletRequest request) {
        return BaseResponse.success(complexPaymentService.syncPaymentStatusReq(request, baseReq));
    }

    @RequestMapping("/payOrder/list")
    @ResponseBody
    public BaseResponse<?> paymentOrderList() {
        return BaseResponse.success(complexPaymentService.getOrderList());
    }


    @RequestMapping("/payerMax/callback")
    @ResponseBody
    public Object payerMaxCallback(@RequestBody Map<String, Object> mapData, HttpServletRequest request) {
        complexPaymentService.notifyCallbackProcess(mapData, PaymentTypeEnum.PAYER_MAX, request);
        return ImmutableMap.of("msg", "Success", "code", "SUCCESS");
    }


    @PostMapping("/paypal/callback")
    @ResponseBody
    public BaseResponse<?> paypalCallback(@RequestBody Map<String, Object> mapData, HttpServletRequest request) {
        PaymentCallbackRspVo callbackRspVo = complexPaymentService.notifyCallbackProcess(mapData, PaymentTypeEnum.PAYPAL, request);
        return BaseResponse.success(callbackRspVo);
    }

    /**
     * 新增支付成功展示消息
     *
     * @return
     */
    @RequestMapping("/success")
    @ResponseBody
    public String PaymentSuccess() {
        return "Pay success,Please return to the app";
    }
}
